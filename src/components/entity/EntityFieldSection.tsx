import React from 'react';
import { Entity } from '@/types/entity';
import { FieldConfig, getFieldsForEntity, getFieldValue } from '@/config/entityFieldMapping';
import FieldRenderer from './FieldRenderer';
import * as LucideIcons from 'lucide-react';

interface EntityFieldSectionProps {
  entity: Entity;
  section: 'overview' | 'features' | 'pricing' | 'contact' | 'technical' | 'company';
  title?: string;
  className?: string;
  showIcons?: boolean;
  layout?: 'list' | 'grid' | 'compact';
}

const EntityFieldSection: React.FC<EntityFieldSectionProps> = ({
  entity,
  section,
  title,
  className = '',
  showIcons = true,
  layout = 'list'
}) => {
  const allFields = getFieldsForEntity(entity);
  const sectionFields = allFields.filter(field => field.section === section);

  // Filter out fields with no value (unless they have a fallback)
  const visibleFields = sectionFields.filter(field => {
    const value = getFieldValue(entity, field);
    return value !== null && value !== undefined && value !== '' || field.fallback;
  });

  if (visibleFields.length === 0) {
    return null;
  }

  const sectionTitles: Record<string, string> = {
    overview: 'Overview',
    features: 'Features & Categories',
    pricing: 'Pricing & Plans',
    contact: 'Contact & Links',
    technical: 'Technical Details',
    company: 'Company Information',
  };

  const displayTitle = title || sectionTitles[section];

  const renderField = (field: FieldConfig) => {
    const IconComponent = showIcons && field.icon
      ? (LucideIcons as Record<string, React.ComponentType<{ className?: string }>>)[field.icon]
      : null;

    const fieldContent = (
      <FieldRenderer 
        entity={entity} 
        field={field}
        className={layout === 'compact' ? 'text-sm' : ''}
      />
    );

    switch (layout) {
      case 'grid':
        return (
          <div key={field.key} className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-2">
              {IconComponent && <IconComponent className="w-4 h-4 text-gray-600 mr-2" />}
              <span className="text-sm font-medium text-gray-700">{field.label}</span>
            </div>
            {fieldContent}
          </div>
        );

      case 'compact':
        return (
          <div key={field.key} className="flex items-center justify-between py-1">
            <div className="flex items-center">
              {IconComponent && <IconComponent className="w-3 h-3 text-gray-500 mr-2" />}
              <span className="text-sm text-gray-600">{field.label}:</span>
            </div>
            <div className="ml-4">
              {fieldContent}
            </div>
          </div>
        );

      default: // list
        return (
          <div key={field.key} className="flex items-start space-x-3 py-3 border-b border-gray-100 last:border-b-0">
            {IconComponent && (
              <div className="flex-shrink-0 mt-0.5">
                <IconComponent className="w-5 h-5 text-gray-500" />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-700 mb-1">
                {field.label}
              </div>
              {fieldContent}
            </div>
          </div>
        );
    }
  };

  const getLayoutClasses = () => {
    switch (layout) {
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 gap-4';
      case 'compact':
        return 'space-y-1';
      default:
        return 'space-y-0';
    }
  };

  return (
    <div className={className}>
      {displayTitle && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {displayTitle}
        </h3>
      )}
      <div className={getLayoutClasses()}>
        {visibleFields.map(renderField)}
      </div>
    </div>
  );
};

export default EntityFieldSection;
