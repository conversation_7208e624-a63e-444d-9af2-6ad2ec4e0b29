import React from 'react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Star, 
  ExternalLink, 
  Check, 
  X, 
  Twitter, 
  Linkedin, 
  Github, 
  Youtube, 
  Facebook, 
  Instagram,
  MessageCircle
} from 'lucide-react';
import { FieldConfig, getFieldValue } from '@/config/entityFieldMapping';
import { Entity } from '@/types/entity';
import { getFieldWithFallback } from '@/utils/entityFallbacks';

interface FieldRendererProps {
  entity: Entity;
  field: FieldConfig;
  className?: string;
}

const FieldRenderer: React.FC<FieldRendererProps> = ({ entity, field, className = '' }) => {
  // Try to get value with smart fallbacks first
  let value = getFieldWithFallback(entity, field.key);

  // If still no value, try the field's custom accessor or direct field access
  if (value === null || value === undefined || value === '') {
    value = getFieldValue(entity, field);
  }

  // Return null if no value and no fallback
  if (value === null || value === undefined || value === '') {
    if (field.fallback) {
      const fallbackValue = typeof field.fallback === 'function' ? field.fallback() : field.fallback;
      return (
        <div className={`text-gray-500 italic ${className}`}>
          {fallbackValue}
        </div>
      );
    }
    return null;
  }

  const renderValue = () => {
    // Use custom formatter if provided
    if (field.formatter) {
      const formatted = field.formatter(value);
      if (React.isValidElement(formatted)) {
        return formatted;
      }
      return <span>{formatted}</span>;
    }

    switch (field.type) {
      case 'text':
        return <span className="text-gray-900">{value}</span>;

      case 'url':
        return (
          <Link 
            href={value} 
            target="_blank" 
            rel="noopener noreferrer"
            className="inline-flex items-center text-indigo-600 hover:text-indigo-800 transition-colors"
          >
            {field.label}
            <ExternalLink className="w-3 h-3 ml-1" />
          </Link>
        );

      case 'email':
        return (
          <Link 
            href={`mailto:${value}`}
            className="text-indigo-600 hover:text-indigo-800 transition-colors"
          >
            {value}
          </Link>
        );

      case 'date':
        return <span className="text-gray-900">{new Date(value).toLocaleDateString()}</span>;

      case 'number':
        return <span className="text-gray-900 font-medium">{value.toLocaleString()}</span>;

      case 'boolean':
        return (
          <div className="inline-flex items-center">
            {value ? (
              <>
                <Check className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-green-700">Yes</span>
              </>
            ) : (
              <>
                <X className="w-4 h-4 text-red-600 mr-1" />
                <span className="text-red-700">No</span>
              </>
            )}
          </div>
        );

      case 'rating':
        return (
          <div className="inline-flex items-center">
            <div className="flex items-center mr-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star
                  key={star}
                  className={`w-4 h-4 ${
                    star <= Math.floor(value) 
                      ? 'text-yellow-400 fill-current' 
                      : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-gray-900 font-medium">{value.toFixed(1)}</span>
          </div>
        );

      case 'array':
        if (!Array.isArray(value) || value.length === 0) {
          return <span className="text-gray-500 italic">None specified</span>;
        }
        return (
          <div className="flex flex-wrap gap-1">
            {value.map((item, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {item}
              </Badge>
            ))}
          </div>
        );

      case 'badge':
        return (
          <Badge variant="outline" className="font-medium">
            {value}
          </Badge>
        );

      case 'social':
        if (!value || typeof value !== 'object') {
          return <span className="text-gray-500 italic">No social links</span>;
        }
        
        const socialIcons: Record<string, React.ComponentType<{ className?: string }>> = {
          twitter: Twitter,
          linkedin: Linkedin,
          github: Github,
          youtube: Youtube,
          facebook: Facebook,
          instagram: Instagram,
          discord: MessageCircle,
        };

        const socialLinks = Object.entries(value)
          .filter(([, url]) => url && typeof url === 'string')
          .slice(0, 5); // Limit to 5 social links

        if (socialLinks.length === 0) {
          return <span className="text-gray-500 italic">No social links</span>;
        }

        return (
          <div className="flex gap-2">
            {socialLinks.map(([platform, url]) => {
              const IconComponent = socialIcons[platform.toLowerCase()];
              return (
                <Button
                  key={platform}
                  variant="outline"
                  size="sm"
                  asChild
                  className="h-8 w-8 p-0"
                >
                  <Link href={url as string} target="_blank" rel="noopener noreferrer">
                    {IconComponent ? (
                      <IconComponent className="w-4 h-4" />
                    ) : (
                      <ExternalLink className="w-4 h-4" />
                    )}
                  </Link>
                </Button>
              );
            })}
          </div>
        );

      case 'price':
        const priceLabels: Record<string, string> = {
          'FREE': 'Free',
          'LOW': '$1 - $50/month',
          'MEDIUM': '$50 - $200/month',
          'HIGH': '$200 - $1000/month',
          'ENTERPRISE': 'Enterprise pricing',
        };
        return (
          <Badge variant="outline" className="font-medium text-green-700 border-green-300">
            {priceLabels[value] || value}
          </Badge>
        );

      default:
        return <span className="text-gray-900">{String(value)}</span>;
    }
  };

  return (
    <div className={className}>
      {renderValue()}
    </div>
  );
};

export default FieldRenderer;
