import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Entity, ToolDetails, CourseDetails, AgencyDetails, ContentCreatorDetails, CommunityDetails, NewsletterDetails } from '@/types/entity';
import { Review } from '@/types/review';
import ReviewItem from './ReviewItem';
import {
  Star, ExternalLink, Tag as TagIcon, Layers as CategoryIcon, Briefcase as EntityTypeIcon,
  Zap, BookOpen, Users, FileText, Megaphone, Users2, Newspaper, Palette, Share2,
  Bookmark, Globe, Mail, Twitter, Github, Linkedin, MessageCircle, CheckCircle,
  MapPin, Calendar, TrendingUp, Sparkles, Code, Settings, Monitor, Shield,
  Cloud, Smartphone, Lock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import ReviewForm, { ReviewFormData } from './ReviewForm';
import { useAuth } from '@/contexts/AuthContext';
import { useBookmarkContext } from '@/contexts/BookmarkContext';
import { useRouter } from 'next/navigation';
import { useShare } from '@/hooks/useShare';

interface DetailedResourceViewProps {
  entity: Entity;
  reviews: Review[];
  onLoadMoreReviews: () => void;
  hasMoreReviews: boolean;
  isLoadingReviews: boolean;
  reviewsTotalCount?: number;
  onSubmitReview: (data: ReviewFormData) => Promise<void>;
  isSubmittingReview: boolean;
  reviewSubmissionError?: string | null;
  reviewSubmissionSuccess?: string | null;
}

// Helper to format field keys for display
const formatFieldKey = (key: string) => {
  return key
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

// Helper to render a list of strings (e.g., key features, prerequisites)
// const renderStringList = (items?: string[], title?: string) => {
//   if (!items || items.length === 0) return null;
//   return (
//     <div className="mt-2">
//       {title && <p className="text-sm font-semibold text-gray-600 mb-1">{title}:</p>}
//       <ul className="list-disc list-inside pl-1 space-y-0.5">
//         {items.map((item, index) => (
//           <li key={index} className="text-sm text-gray-600">{item}</li>
//         ))}
//       </ul>
//     </div>
//   );
// };

const DetailedResourceView: React.FC<DetailedResourceViewProps> = ({
  entity,
  reviews,
  onLoadMoreReviews,
  hasMoreReviews,
  isLoadingReviews,
  reviewsTotalCount,
  onSubmitReview,
  isSubmittingReview,
  reviewSubmissionError,
  reviewSubmissionSuccess
}) => {
  const { session } = useAuth();
  const router = useRouter();
  const { isBookmarked, toggleBookmark } = useBookmarkContext();
  const { shareEntity } = useShare();
  const [isBookmarkLoading, setIsBookmarkLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const fallbackImage = '/images/placeholder-logo.png';
  const entityName = entity?.name || 'Unnamed Entity';

  // Data validation helpers
  const isValidUrl = (url: string | null | undefined): boolean => {
    if (!url) return false;
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const isValidEmail = (email: string | null | undefined): boolean => {
    if (!email) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // const sanitizeText = (text: string | null | undefined): string => {
  //   if (!text) return '';
  //   return text.trim().replace(/\s+/g, ' ');
  // };

  const validateAndFormatYear = (year: number | null | undefined): string | null => {
    if (!year) return null;
    const currentYear = new Date().getFullYear();
    if (year < 1900 || year > currentYear) return null;
    return year.toString();
  };

  const handleBookmark = async () => {
    if (!session) {
      router.push('/login');
      return;
    }

    setIsBookmarkLoading(true);
    try {
      await toggleBookmark(entity.id);
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
      // You could show a toast notification here
    } finally {
      setIsBookmarkLoading(false);
    }
  };

  const handleShare = async () => {
    try {
      await shareEntity(entity);
    } catch (error) {
      console.error('Failed to share entity:', error);
      // You could show a toast notification here
    }
  };

  // Helper functions for dynamic content
  const hasCompanyInfo = () => {
    return entity.foundedYear ||
           entity.locationSummary ||
           entity.details?.employeeCountRange ||
           entity.details?.fundingStage;
  };

  const hasSocialLinks = () => {
    return entity.socialLinks && Object.values(entity.socialLinks).some(link => link);
  };

  const hasIntegrations = () => {
    return entity.details?.integrations && entity.details.integrations.length > 0;
  };

  const hasSupportOptions = () => {
    return entity.details?.supportChannels?.length > 0 ||
           entity.details?.supportEmail ||
           entity.details?.hasLiveChat ||
           entity.details?.communityUrl ||
           entity.documentationUrl ||
           entity.contactUrl;
  };

  const hasFeatures = () => {
    return (entity.features && entity.features.length > 0) ||
           (entity.details?.keyFeatures && entity.details.keyFeatures.length > 0);
  };

  // const hasUseCases = () => {
  //   return entity.details?.useCases && entity.details.useCases.length > 0;
  // };

  const hasPricingInfo = () => {
    return entity.details?.pricingModel ||
           entity.details?.priceRange ||
           entity.details?.pricingDetails ||
           entity.details?.pricingUrl ||
           entity.details?.hasFreeTier;
  };

  // Generate fallback use cases based on entity type and categories
  const getFallbackUseCases = () => {
    const entityType = entity.entityType?.slug;
    const categories = entity.categories?.map(cat => cat.name.toLowerCase()) || [];

    const useCaseMap: Record<string, string[]> = {
      'tool': ['Automation', 'Productivity', 'Data Analysis', 'Content Creation'],
      'course': ['Learning', 'Skill Development', 'Professional Growth', 'Certification'],
      'agency': ['Consulting', 'Implementation', 'Strategy', 'Support'],
      'content-creator': ['Content Creation', 'Education', 'Entertainment', 'Community Building'],
      'community': ['Networking', 'Knowledge Sharing', 'Support', 'Collaboration'],
      'newsletter': ['Information', 'Updates', 'Industry News', 'Learning']
    };

    // Get base use cases from entity type
    let useCases = useCaseMap[entityType] || ['General Use', 'Business Applications'];

    // Enhance based on categories
    if (categories.includes('ai') || categories.includes('artificial intelligence')) {
      useCases = ['AI Integration', 'Machine Learning', 'Automation', 'Data Analysis'];
    }
    if (categories.includes('marketing')) {
      useCases = ['Marketing Campaigns', 'Lead Generation', 'Analytics', 'Content Marketing'];
    }
    if (categories.includes('development') || categories.includes('programming')) {
      useCases = ['Software Development', 'Code Generation', 'Testing', 'Deployment'];
    }

    return useCases.slice(0, 4); // Limit to 4 use cases
  };

  const renderStars = (rating: number) => {
    const stars = [];
    if (typeof rating !== 'number') return <span className="text-gray-500 text-sm">Not rated</span>;
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-5 h-5 ${i <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
          fill={i <= rating ? 'currentColor' : 'none'}
        />,
      );
    }
    return stars;
  };

  const renderEntitySpecificDetails = () => {
    if (!entity.details) return null;

    let detailsContent = null;
    let icon = <Palette className="w-5 h-5 mr-2 text-indigo-600" />; // Default icon
    let detailsTitle = `${entity.entityType?.name || 'Additional'} Details`;

    // Helper function to render a detail card section
    const renderDetailCard = (title: string, icon: React.ReactNode, children: React.ReactNode, className?: string) => (
      <div className={`bg-white rounded-xl shadow-sm border border-gray-100 p-6 ${className || ''}`}>
        <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          {icon}
          {title}
        </h4>
        <div className="space-y-3">
          {children}
        </div>
      </div>
    );

    // Helper function to render a detail item with icon
    const renderDetailItem = (label: string, value: string | boolean, icon?: React.ReactNode) => (
      <div className="flex items-center text-sm">
        {icon && <div className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0">{icon}</div>}
        <span className="text-gray-600">
          <span className="font-medium text-gray-700">{label}:</span> {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value}
        </span>
      </div>
    );

    // Helper function to render badge list
    const renderBadgeList = (items: string[] | undefined, variant: 'default' | 'secondary' | 'outline' = 'outline') => {
      if (!items || items.length === 0) return null;
      return (
        <div className="flex flex-wrap gap-2">
          {items.map((item, index) => (
            <Badge key={index} variant={variant} className="text-xs">
              {item}
            </Badge>
          ))}
        </div>
      );
    };

    switch (entity.entityType?.slug) {
      case 'tool':
      case 'ai-tool':
        const toolDetails = entity.details as ToolDetails;
        icon = <Zap className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Tool Specifications";

        const toolCards = [];

        // Technical Specifications Card
        const techSpecs = [];
        if (toolDetails.technical_level) techSpecs.push(renderDetailItem('Technical Level', toolDetails.technical_level, <TrendingUp className="w-4 h-4" />));
        if (toolDetails.learning_curve) techSpecs.push(renderDetailItem('Learning Curve', toolDetails.learning_curve, <BookOpen className="w-4 h-4" />));
        if (toolDetails.customization_level) techSpecs.push(renderDetailItem('Customization Level', toolDetails.customization_level, <Settings className="w-4 h-4" />));
        if (typeof toolDetails.api_available === 'boolean') techSpecs.push(renderDetailItem('API Available', toolDetails.api_available, <Code className="w-4 h-4" />));
        if (typeof toolDetails.self_hosted_option === 'boolean') techSpecs.push(renderDetailItem('Self-Hosted Option', toolDetails.self_hosted_option, <Cloud className="w-4 h-4" />));

        if (techSpecs.length > 0) {
          toolCards.push(renderDetailCard('Technical Specifications', <Code className="w-5 h-5 mr-2 text-indigo-600" />, techSpecs));
        }

        // Features & Capabilities Card
        const featuresContent = [];
        if (toolDetails.key_features && toolDetails.key_features.length > 0) {
          featuresContent.push(
            <div key="key-features">
              <p className="font-medium text-gray-700 mb-2">Key Features</p>
              {renderBadgeList(toolDetails.key_features, 'default')}
            </div>
          );
        }
        if (toolDetails.use_cases && toolDetails.use_cases.length > 0) {
          featuresContent.push(
            <div key="use-cases">
              <p className="font-medium text-gray-700 mb-2">Use Cases</p>
              {renderBadgeList(toolDetails.use_cases, 'secondary')}
            </div>
          );
        }
        if (toolDetails.integrations && toolDetails.integrations.length > 0) {
          featuresContent.push(
            <div key="integrations">
              <p className="font-medium text-gray-700 mb-2">Integrations</p>
              {renderBadgeList(toolDetails.integrations, 'outline')}
            </div>
          );
        }

        if (featuresContent.length > 0) {
          toolCards.push(renderDetailCard('Features & Capabilities', <Star className="w-5 h-5 mr-2 text-amber-600" />, featuresContent));
        }

        // Platform & Compatibility Card
        const platformSpecs = [];
        if (toolDetails.programming_languages && toolDetails.programming_languages.length > 0) {
          platformSpecs.push(
            <div key="programming-languages">
              <p className="font-medium text-gray-700 mb-2">Programming Languages</p>
              {renderBadgeList(toolDetails.programming_languages, 'outline')}
            </div>
          );
        }
        if (toolDetails.supported_os && toolDetails.supported_os.length > 0) {
          platformSpecs.push(
            <div key="supported-os">
              <p className="font-medium text-gray-700 mb-2">Supported Operating Systems</p>
              {renderBadgeList(toolDetails.supported_os, 'outline')}
            </div>
          );
        }
        if (typeof toolDetails.mobile_support === 'boolean') {
          platformSpecs.push(renderDetailItem('Mobile Support', toolDetails.mobile_support, <Smartphone className="w-4 h-4" />));
        }

        if (platformSpecs.length > 0) {
          toolCards.push(renderDetailCard('Platform & Compatibility', <Monitor className="w-5 h-5 mr-2 text-green-600" />, platformSpecs));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {toolCards}
          </div>
        );
        break;
      case 'course':
        const courseDetails = entity.details as CourseDetails;
        icon = <BookOpen className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Course Information";

        const courseCards = [];

        // Course Details Card
        const courseInfo = [];
        if (courseDetails.instructor_name) courseInfo.push(renderDetailItem('Instructor', courseDetails.instructor_name, <Users className="w-4 h-4" />));
        if (courseDetails.duration_text) courseInfo.push(renderDetailItem('Duration', courseDetails.duration_text, <Calendar className="w-4 h-4" />));
        if (courseDetails.level) courseInfo.push(renderDetailItem('Level', courseDetails.level, <TrendingUp className="w-4 h-4" />));
        if (courseDetails.language) courseInfo.push(renderDetailItem('Language', courseDetails.language, <Globe className="w-4 h-4" />));
        if (typeof courseDetails.certificate_available === 'boolean') courseInfo.push(renderDetailItem('Certificate', courseDetails.certificate_available ? 'Available' : 'Not Available', <CheckCircle className="w-4 h-4" />));

        if (courseInfo.length > 0) {
          courseCards.push(renderDetailCard('Course Details', <BookOpen className="w-5 h-5 mr-2 text-indigo-600" />, courseInfo));
        }

        // Learning Content Card
        const learningContent = [];
        if (courseDetails.prerequisites && courseDetails.prerequisites.length > 0) {
          learningContent.push(
            <div key="prerequisites">
              <p className="font-medium text-gray-700 mb-2">Prerequisites</p>
              {renderBadgeList(courseDetails.prerequisites, 'outline')}
            </div>
          );
        }
        if (courseDetails.learning_outcomes && courseDetails.learning_outcomes.length > 0) {
          learningContent.push(
            <div key="learning-outcomes">
              <p className="font-medium text-gray-700 mb-2">Learning Outcomes</p>
              {renderBadgeList(courseDetails.learning_outcomes, 'secondary')}
            </div>
          );
        }

        if (learningContent.length > 0) {
          courseCards.push(renderDetailCard('Learning Content', <Sparkles className="w-5 h-5 mr-2 text-amber-600" />, learningContent));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {courseCards}
          </div>
        );
        break;
      case 'agency':
        const agencyDetails = entity.details as AgencyDetails;
        icon = <EntityTypeIcon className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Agency Overview";

        const agencyCards = [];

        // Agency Info Card
        const agencyInfo = [];
        if (agencyDetails.team_size) agencyInfo.push(renderDetailItem('Team Size', agencyDetails.team_size, <Users className="w-4 h-4" />));
        if (agencyDetails.region_served) agencyInfo.push(renderDetailItem('Region Served', agencyDetails.region_served, <MapPin className="w-4 h-4" />));
        if (agencyDetails.contact_email) {
          agencyInfo.push(
            <div className="flex items-center text-sm">
              <Mail className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Contact:</span>
                <a href={`mailto:${agencyDetails.contact_email}`} className="text-indigo-600 hover:text-indigo-800 ml-1">
                  {agencyDetails.contact_email}
                </a>
              </span>
            </div>
          );
        }
        if (agencyDetails.portfolio_url) {
          agencyInfo.push(
            <div className="flex items-center text-sm">
              <ExternalLink className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Portfolio:</span>
                <a href={agencyDetails.portfolio_url} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800 ml-1">
                  View Portfolio
                </a>
              </span>
            </div>
          );
        }

        if (agencyInfo.length > 0) {
          agencyCards.push(renderDetailCard('Agency Information', <EntityTypeIcon className="w-5 h-5 mr-2 text-indigo-600" />, agencyInfo));
        }

        // Services & Specializations Card
        const servicesContent = [];
        if (agencyDetails.services_offered && agencyDetails.services_offered.length > 0) {
          servicesContent.push(
            <div key="services">
              <p className="font-medium text-gray-700 mb-2">Services Offered</p>
              {renderBadgeList(agencyDetails.services_offered, 'default')}
            </div>
          );
        }
        if (agencyDetails.specializations && agencyDetails.specializations.length > 0) {
          servicesContent.push(
            <div key="specializations">
              <p className="font-medium text-gray-700 mb-2">Specializations</p>
              {renderBadgeList(agencyDetails.specializations, 'secondary')}
            </div>
          );
        }

        if (servicesContent.length > 0) {
          agencyCards.push(renderDetailCard('Services & Expertise', <Star className="w-5 h-5 mr-2 text-amber-600" />, servicesContent));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {agencyCards}
          </div>
        );
        break;
      case 'content-creator':
        const creatorDetails = entity.details as ContentCreatorDetails;
        icon = <Megaphone className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Creator Profile";

        const creatorCards = [];

        // Creator Info Card
        const creatorInfo = [];
        if (creatorDetails.platform) creatorInfo.push(renderDetailItem('Main Platform', creatorDetails.platform, <Monitor className="w-4 h-4" />));
        if (creatorDetails.subscriber_count) creatorInfo.push(renderDetailItem('Subscribers/Followers', creatorDetails.subscriber_count, <Users className="w-4 h-4" />));
        if (creatorDetails.platform_url) {
          creatorInfo.push(
            <div className="flex items-center text-sm">
              <ExternalLink className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Platform:</span>
                <a href={creatorDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800 ml-1">
                  View Platform
                </a>
              </span>
            </div>
          );
        }
        if (creatorDetails.collaboration_email) {
          creatorInfo.push(
            <div className="flex items-center text-sm">
              <Mail className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Collaborations:</span>
                <a href={`mailto:${creatorDetails.collaboration_email}`} className="text-indigo-600 hover:text-indigo-800 ml-1">
                  {creatorDetails.collaboration_email}
                </a>
              </span>
            </div>
          );
        }

        if (creatorInfo.length > 0) {
          creatorCards.push(renderDetailCard('Creator Information', <Megaphone className="w-5 h-5 mr-2 text-indigo-600" />, creatorInfo));
        }

        // Content & Work Card
        const contentInfo = [];
        if (creatorDetails.content_focus && creatorDetails.content_focus.length > 0) {
          contentInfo.push(
            <div key="content-focus">
              <p className="font-medium text-gray-700 mb-2">Content Focus</p>
              {renderBadgeList(creatorDetails.content_focus, 'secondary')}
            </div>
          );
        }
        if (creatorDetails.sample_work_links && creatorDetails.sample_work_links.length > 0) {
          contentInfo.push(
            <div key="sample-work">
              <p className="font-medium text-gray-700 mb-2">Sample Work</p>
              <div className="space-y-2">
                {creatorDetails.sample_work_links.map((link, index) => (
                  <a
                    key={index}
                    href={link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800"
                  >
                    Sample Link {index + 1}
                    <ExternalLink className="w-3 h-3 ml-1" />
                  </a>
                ))}
              </div>
            </div>
          );
        }

        if (contentInfo.length > 0) {
          creatorCards.push(renderDetailCard('Content & Portfolio', <FileText className="w-5 h-5 mr-2 text-amber-600" />, contentInfo));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {creatorCards}
          </div>
        );
        break;
      case 'community':
        const communityDetails = entity.details as CommunityDetails;
        icon = <Users2 className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Community Hub";

        const communityCards = [];

        // Community Info Card
        const communityInfo = [];
        if (communityDetails.platform_name) communityInfo.push(renderDetailItem('Platform', communityDetails.platform_name, <Monitor className="w-4 h-4" />));
        if (communityDetails.member_count) communityInfo.push(renderDetailItem('Members', communityDetails.member_count, <Users className="w-4 h-4" />));
        if (communityDetails.moderator_info) communityInfo.push(renderDetailItem('Moderator Info', communityDetails.moderator_info, <Shield className="w-4 h-4" />));
        if (communityDetails.entry_requirements) communityInfo.push(renderDetailItem('Entry Requirements', communityDetails.entry_requirements, <Lock className="w-4 h-4" />));
        if (communityDetails.platform_url) {
          communityInfo.push(
            <div className="flex items-center text-sm">
              <ExternalLink className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Community:</span>
                <a href={communityDetails.platform_url} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800 ml-1">
                  Join Community
                </a>
              </span>
            </div>
          );
        }

        if (communityInfo.length > 0) {
          communityCards.push(renderDetailCard('Community Information', <Users2 className="w-5 h-5 mr-2 text-indigo-600" />, communityInfo));
        }

        // Topics Card
        if (communityDetails.main_topics && communityDetails.main_topics.length > 0) {
          const topicsContent = [
            <div key="main-topics">
              <p className="font-medium text-gray-700 mb-2">Main Topics</p>
              {renderBadgeList(communityDetails.main_topics, 'secondary')}
            </div>
          ];
          communityCards.push(renderDetailCard('Discussion Topics', <MessageCircle className="w-5 h-5 mr-2 text-amber-600" />, topicsContent));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {communityCards}
          </div>
        );
        break;
      case 'newsletter':
        const newsletterDetails = entity.details as NewsletterDetails;
        icon = <Newspaper className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Newsletter Info";

        const newsletterCards = [];

        // Newsletter Info Card
        const newsletterInfo = [];
        if (newsletterDetails.author_name) newsletterInfo.push(renderDetailItem('Author', newsletterDetails.author_name, <Users className="w-4 h-4" />));
        if (newsletterDetails.publication_schedule) newsletterInfo.push(renderDetailItem('Schedule', newsletterDetails.publication_schedule, <Calendar className="w-4 h-4" />));
        if (newsletterDetails.target_audience) newsletterInfo.push(renderDetailItem('Target Audience', newsletterDetails.target_audience, <Users2 className="w-4 h-4" />));

        // Add links
        if (newsletterDetails.archive_url) {
          newsletterInfo.push(
            <div className="flex items-center text-sm">
              <ExternalLink className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Archive:</span>
                <a href={newsletterDetails.archive_url} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800 ml-1">
                  View Archive
                </a>
              </span>
            </div>
          );
        }
        if (newsletterDetails.subscription_link) {
          newsletterInfo.push(
            <div className="flex items-center text-sm">
              <ExternalLink className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
              <span className="text-gray-600">
                <span className="font-medium text-gray-700">Subscribe:</span>
                <a href={newsletterDetails.subscription_link} target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:text-indigo-800 ml-1">
                  Subscribe Here
                </a>
              </span>
            </div>
          );
        }

        if (newsletterInfo.length > 0) {
          newsletterCards.push(renderDetailCard('Newsletter Information', <Newspaper className="w-5 h-5 mr-2 text-indigo-600" />, newsletterInfo));
        }

        // Topics Card
        if (newsletterDetails.topics_covered && newsletterDetails.topics_covered.length > 0) {
          const topicsContent = [
            <div key="topics-covered">
              <p className="font-medium text-gray-700 mb-2">Topics Covered</p>
              {renderBadgeList(newsletterDetails.topics_covered, 'secondary')}
            </div>
          ];
          newsletterCards.push(renderDetailCard('Content Topics', <FileText className="w-5 h-5 mr-2 text-amber-600" />, topicsContent));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {newsletterCards}
          </div>
        );
        break;
      case 'platform':
        // Platform entities can use the same structure as tools since they're similar
        const platformDetails = entity.details as ToolDetails;
        icon = <Monitor className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Platform Specifications";

        const platformCards = [];

        // Technical Specifications Card
        const platformTechSpecs = [];
        if (platformDetails.technical_level) platformTechSpecs.push(renderDetailItem('Technical Level', platformDetails.technical_level, <TrendingUp className="w-4 h-4" />));
        if (platformDetails.learning_curve) platformTechSpecs.push(renderDetailItem('Learning Curve', platformDetails.learning_curve, <BookOpen className="w-4 h-4" />));
        if (platformDetails.customization_level) platformTechSpecs.push(renderDetailItem('Customization Level', platformDetails.customization_level, <Settings className="w-4 h-4" />));
        if (typeof platformDetails.api_available === 'boolean') platformTechSpecs.push(renderDetailItem('API Available', platformDetails.api_available, <Code className="w-4 h-4" />));
        if (typeof platformDetails.self_hosted_option === 'boolean') platformTechSpecs.push(renderDetailItem('Self-Hosted Option', platformDetails.self_hosted_option, <Cloud className="w-4 h-4" />));

        if (platformTechSpecs.length > 0) {
          platformCards.push(renderDetailCard('Technical Specifications', <Code className="w-5 h-5 mr-2 text-indigo-600" />, platformTechSpecs));
        }

        // Features & Capabilities Card
        const platformFeaturesContent = [];
        if (platformDetails.key_features && platformDetails.key_features.length > 0) {
          platformFeaturesContent.push(
            <div key="key-features">
              <p className="font-medium text-gray-700 mb-2">Key Features</p>
              {renderBadgeList(platformDetails.key_features, 'default')}
            </div>
          );
        }
        if (platformDetails.use_cases && platformDetails.use_cases.length > 0) {
          platformFeaturesContent.push(
            <div key="use-cases">
              <p className="font-medium text-gray-700 mb-2">Use Cases</p>
              {renderBadgeList(platformDetails.use_cases, 'secondary')}
            </div>
          );
        }
        if (platformDetails.integrations && platformDetails.integrations.length > 0) {
          platformFeaturesContent.push(
            <div key="integrations">
              <p className="font-medium text-gray-700 mb-2">Integrations</p>
              {renderBadgeList(platformDetails.integrations, 'outline')}
            </div>
          );
        }

        if (platformFeaturesContent.length > 0) {
          platformCards.push(renderDetailCard('Features & Capabilities', <Star className="w-5 h-5 mr-2 text-amber-600" />, platformFeaturesContent));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-2">
            {platformCards}
          </div>
        );
        break;
      case 'research-paper':
        // Research papers will use a simplified structure focusing on academic details
        icon = <FileText className="w-5 h-5 mr-2 text-indigo-600" />;
        detailsTitle = "Research Paper Details";

        const paperCards = [];
        const paperDetails = [];

        // Extract relevant details from the entity details
        Object.entries(entity.details).forEach(([key, value]) => {
          if (!value) return;

          const formattedKey = formatFieldKey(key);
          const stringValue = String(value);

          // Add appropriate icons based on the field type
          let fieldIcon = <FileText className="w-4 h-4" />;
          if (key.includes('author') || key.includes('researcher')) {
            fieldIcon = <Users className="w-4 h-4" />;
          } else if (key.includes('date') || key.includes('year') || key.includes('published')) {
            fieldIcon = <Calendar className="w-4 h-4" />;
          } else if (key.includes('citation') || key.includes('doi')) {
            fieldIcon = <ExternalLink className="w-4 h-4" />;
          }

          paperDetails.push(renderDetailItem(formattedKey, stringValue, fieldIcon));
        });

        if (paperDetails.length > 0) {
          paperCards.push(renderDetailCard('Paper Information', <FileText className="w-5 h-5 mr-2 text-indigo-600" />, paperDetails));
        }

        detailsContent = (
          <div className="grid gap-6 md:grid-cols-1">
            {paperCards}
          </div>
        );
        break;
      default:
        if (Object.keys(entity.details).length > 0) {
          detailsTitle = `Additional Details`;

          // Group details into logical categories for better organization
          const miscDetails = [];
          const technicalDetails = [];
          const businessDetails = [];

          Object.entries(entity.details).forEach(([key, value]) => {
            if (!value) return; // Skip empty values

            const formattedKey = formatFieldKey(key);
            const stringValue = String(value);

            // Categorize based on key patterns
            if (key.includes('technical') || key.includes('api') || key.includes('programming') || key.includes('framework')) {
              technicalDetails.push(renderDetailItem(formattedKey, stringValue, <Code className="w-4 h-4" />));
            } else if (key.includes('pricing') || key.includes('business') || key.includes('revenue') || key.includes('funding')) {
              businessDetails.push(renderDetailItem(formattedKey, stringValue, <TrendingUp className="w-4 h-4" />));
            } else {
              miscDetails.push(renderDetailItem(formattedKey, stringValue, <FileText className="w-4 h-4" />));
            }
          });

          const defaultCards = [];

          if (technicalDetails.length > 0) {
            defaultCards.push(renderDetailCard('Technical Information', <Code className="w-5 h-5 mr-2 text-indigo-600" />, technicalDetails));
          }

          if (businessDetails.length > 0) {
            defaultCards.push(renderDetailCard('Business Information', <TrendingUp className="w-5 h-5 mr-2 text-green-600" />, businessDetails));
          }

          if (miscDetails.length > 0) {
            defaultCards.push(renderDetailCard('General Information', <FileText className="w-5 h-5 mr-2 text-gray-600" />, miscDetails));
          }

          detailsContent = (
            <div className="grid gap-6 md:grid-cols-2">
              {defaultCards}
            </div>
          );
        }
        break;
    }

    if (!detailsContent) return null;

    return (
      <div className="mt-8">
        <h3 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
          {icon} {detailsTitle}
        </h3>
        {detailsContent}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/browse">Browse</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{entityName}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Logo and Basic Info */}
            <div className="flex flex-col sm:flex-row gap-6 lg:flex-1">
              <div className="relative w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl flex items-center justify-center p-4 shadow-sm">
                <Image
                  src={entity.logoUrl || fallbackImage}
                  alt={`${entityName} logo`}
                  width={128}
                  height={128}
                  className="rounded-xl object-contain"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">{entityName}</h1>
                    <p className="text-lg text-gray-600 mb-4">{entity.description}</p>
                  </div>
                </div>

                {/* Rating and Metadata */}
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">{renderStars(entity.avgRating)}</div>
                    <span className="text-sm font-medium text-gray-900">{entity.avgRating || 0}</span>
                    <span className="text-sm text-gray-500">({entity.reviewCount} reviews)</span>
                  </div>

                  <Badge variant="secondary" className="bg-indigo-100 text-indigo-700">
                    <EntityTypeIcon className="w-3 h-3 mr-1" />
                    {entity.entityType?.name || 'Tool'}
                  </Badge>

                  <Badge variant="outline">
                    <CategoryIcon className="w-3 h-3 mr-1" />
                    {entity.categories?.[0]?.name || 'AI'}
                  </Badge>

                  {entity.tags && entity.tags.slice(0, 2).map((tag) => (
                    <Badge key={tag.id} variant="outline" className="text-xs">
                      {tag.name}
                    </Badge>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  {entity.websiteUrl && (
                    <Button asChild size="lg" className="bg-indigo-600 hover:bg-indigo-700">
                      <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                        <Globe className="w-4 h-4 mr-2" />
                        Visit Website
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleBookmark}
                    disabled={isBookmarkLoading}
                  >
                    {isBookmarkLoading ? (
                      <div className="w-4 h-4 mr-2 border border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                    ) : (
                      <Bookmark className={`w-4 h-4 mr-2 ${isBookmarked(entity.id) ? 'fill-current' : ''}`} />
                    )}
                    {isBookmarked(entity.id) ? 'Saved' : 'Save'}
                  </Button>

                  <Button variant="outline" size="lg" onClick={handleShare}>
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Main Content Area */}
          <div className="lg:flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-8">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="pricing">Pricing</TabsTrigger>
                <TabsTrigger value="reviews">Reviews</TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-8">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">About {entityName}</h2>
                  <div className="prose max-w-none">
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {entity.description || 'No description available.'}
                    </p>
                  </div>

                  {/* Entity Specific Details */}
                  {renderEntitySpecificDetails()}

                  {/* Use Cases - Dynamic with Fallbacks */}
                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Use Cases</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {(entity.details?.useCases || getFallbackUseCases()).map((useCase, index) => (
                        <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                          <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{useCase}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tags */}
                  {entity.tags && entity.tags.length > 0 && (
                    <div className="mt-8">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {entity.tags.map((tag) => (
                          <Badge key={tag.id} variant="secondary" className="bg-gray-100 text-gray-700">
                            <TagIcon className="w-3 h-3 mr-1" />
                            {tag.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Features Tab - Dynamic */}
              <TabsContent value="features" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>
                  {hasFeatures() ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Render entity.features first */}
                      {entity.features?.map((feature) => (
                        <div key={feature.id} className="group flex items-start p-5 border border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-sm transition-all duration-200">
                          <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-indigo-200 group-hover:to-indigo-300 transition-colors">
                            <Sparkles className="w-5 h-5 text-indigo-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 mb-2">{feature.name}</h3>
                            {feature.description && (
                              <p className="text-sm text-gray-600 leading-relaxed">{feature.description}</p>
                            )}
                          </div>
                        </div>
                      ))}
                      {/* Render entity.details.keyFeatures if available */}
                      {entity.details?.keyFeatures?.map((feature, index) => (
                        <div key={`key-feature-${index}`} className="group flex items-start p-5 border border-gray-200 rounded-xl hover:border-indigo-300 hover:shadow-sm transition-all duration-200">
                          <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-indigo-200 group-hover:to-indigo-300 transition-colors">
                            <Sparkles className="w-5 h-5 text-indigo-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900">{feature}</h3>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Sparkles className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No features information available for {entityName}.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Pricing Tab - Dynamic */}
              <TabsContent value="pricing" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Pricing Information</h2>
                  {hasPricingInfo() ? (
                    <div className="space-y-6">
                      {/* Pricing Model */}
                      {entity.details?.pricingModel && (
                        <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                          <h3 className="text-lg font-semibold text-gray-900 mb-3">Pricing Model</h3>
                          <Badge variant="secondary" className="text-sm px-3 py-1">
                            {entity.details.pricingModel.replace('_', ' ')}
                          </Badge>
                        </div>
                      )}

                      {/* Price Range */}
                      {entity.details?.priceRange && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Price Range</h3>
                          <Badge variant="outline" className="text-sm">
                            {entity.details.priceRange}
                          </Badge>
                        </div>
                      )}

                      {/* Free Tier */}
                      {entity.details?.hasFreeTier && (
                        <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                          <div className="flex items-center">
                            <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                            <span className="text-green-800 font-medium">Free tier available</span>
                          </div>
                        </div>
                      )}

                      {/* Pricing Details */}
                      {entity.details?.pricingDetails && (
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Details</h3>
                          <p className="text-gray-600">{entity.details.pricingDetails}</p>
                        </div>
                      )}

                      {/* Pricing URL */}
                      {entity.details?.pricingUrl && (
                        <div className="text-center">
                          <Button asChild className="bg-indigo-600 hover:bg-indigo-700">
                            <Link href={entity.details.pricingUrl} target="_blank" rel="noopener noreferrer">
                              View Detailed Pricing
                              <ExternalLink className="w-4 h-4 ml-2" />
                            </Link>
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl">💰</span>
                      </div>
                      <p className="text-gray-600">No pricing information available for {entityName}.</p>
                      {entity.websiteUrl && (
                        <Button asChild variant="outline" className="mt-4">
                          <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                            Visit Website for Pricing
                            <ExternalLink className="w-4 h-4 ml-2" />
                          </Link>
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Reviews Tab */}
              <TabsContent value="reviews" className="space-y-6">
                <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900">
                      User Reviews ({reviews?.length || 0}
                      {reviewsTotalCount && reviewsTotalCount > (reviews?.length || 0) ? ` of ${reviewsTotalCount}` : ''})
                    </h2>
                  </div>

                  {reviews && reviews.length > 0 ? (
                    <div className="space-y-6">
                      {reviews.map((review) => (
                        <ReviewItem key={review.id} review={review} />
                      ))}
                    </div>
                  ) : (
                    !isLoadingReviews && (
                      <div className="text-center py-12">
                        <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-600">No reviews yet for {entityName}. Be the first to share your thoughts!</p>
                      </div>
                    )
                  )}

                  {isLoadingReviews && (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                      <p className="text-gray-500 mt-2">Loading reviews...</p>
                    </div>
                  )}

                  {hasMoreReviews && !isLoadingReviews && (
                    <div className="mt-8 text-center">
                      <Button onClick={onLoadMoreReviews} variant="outline" size="lg">
                        Load More Reviews
                      </Button>
                    </div>
                  )}

                  {/* Write a Review Section */}
                  {session ? (
                    <div className="mt-8 pt-8 border-t border-gray-200">
                      <h3 className="text-lg font-semibold text-gray-900 mb-6">Write a Review for {entityName}</h3>
                      <ReviewForm
                        entityId={entity.id}
                        onSubmitReview={onSubmitReview}
                        isSubmitting={isSubmittingReview}
                        formError={reviewSubmissionError}
                        formSuccess={reviewSubmissionSuccess}
                      />
                    </div>
                  ) : (
                    <div className="mt-8 pt-8 border-t border-gray-200 text-center">
                      <p className="text-gray-600">
                        Please <Link href="/login" className="text-indigo-600 hover:text-indigo-700 font-medium">log in</Link> to write a review.
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="lg:w-80 space-y-8">
            {/* Quick Actions - Dynamic */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                {entity.websiteUrl && (
                  <Button asChild className="w-full bg-indigo-600 hover:bg-indigo-700">
                    <Link href={entity.websiteUrl} target="_blank" rel="noopener noreferrer">
                      <Globe className="w-4 h-4 mr-2" />
                      Visit Website
                    </Link>
                  </Button>
                )}
                {isValidEmail(entity.details?.supportEmail) && (
                  <Button asChild variant="outline" className="w-full">
                    <Link href={`mailto:${entity.details.supportEmail}`}>
                      <Mail className="w-4 h-4 mr-2" />
                      Contact Support
                    </Link>
                  </Button>
                )}
                {entity.contactUrl && (
                  <Button asChild variant="outline" className="w-full">
                    <Link href={entity.contactUrl} target="_blank" rel="noopener noreferrer">
                      <Mail className="w-4 h-4 mr-2" />
                      Contact
                    </Link>
                  </Button>
                )}
                {entity.documentationUrl && (
                  <Button asChild variant="outline" className="w-full">
                    <Link href={entity.documentationUrl} target="_blank" rel="noopener noreferrer">
                      <FileText className="w-4 h-4 mr-2" />
                      Documentation
                    </Link>
                  </Button>
                )}
              </div>
            </div>

            {/* Company Info - Dynamic */}
            {hasCompanyInfo() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Company Info</h3>
                <div className="space-y-3">
                  {validateAndFormatYear(entity.foundedYear) && (
                    <div className="flex items-center text-sm">
                      <Calendar className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">Founded {validateAndFormatYear(entity.foundedYear)}</span>
                    </div>
                  )}
                  {entity.details?.employeeCountRange && (
                    <div className="flex items-center text-sm">
                      <Users className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">{entity.details.employeeCountRange} employees</span>
                    </div>
                  )}
                  {entity.details?.fundingStage && (
                    <div className="flex items-center text-sm">
                      <TrendingUp className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">{entity.details.fundingStage} funding</span>
                    </div>
                  )}
                  {entity.locationSummary && (
                    <div className="flex items-center text-sm">
                      <MapPin className="w-4 h-4 text-gray-400 mr-3" />
                      <span className="text-gray-600">{entity.locationSummary}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Social Links - Dynamic */}
            {hasSocialLinks() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Connect</h3>
                <div className="flex flex-wrap gap-3">
                  {isValidUrl(entity.socialLinks?.twitter) && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                        <Twitter className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                  {isValidUrl(entity.socialLinks?.github) && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.github} target="_blank" rel="noopener noreferrer">
                        <Github className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                  {isValidUrl(entity.socialLinks?.linkedin) && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                        <Linkedin className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.youtube && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.youtube} target="_blank" rel="noopener noreferrer">
                        <span className="w-4 h-4 text-red-600">▶</span>
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.facebook && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.facebook} target="_blank" rel="noopener noreferrer">
                        <span className="w-4 h-4 text-blue-600">f</span>
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.instagram && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.instagram} target="_blank" rel="noopener noreferrer">
                        <span className="w-4 h-4 text-pink-600">📷</span>
                      </Link>
                    </Button>
                  )}
                  {entity.socialLinks?.discord && (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={entity.socialLinks.discord} target="_blank" rel="noopener noreferrer">
                        <MessageCircle className="w-4 h-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            )}

            {/* Integrations - Dynamic */}
            {hasIntegrations() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Integrations</h3>
                <div className="flex flex-wrap gap-2">
                  {entity.details?.integrations?.map((integration, index) => (
                    <Badge key={index} variant="secondary" className="bg-gray-100 text-gray-700">
                      {integration}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Support Options - Dynamic */}
            {hasSupportOptions() && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Support</h3>
                <div className="space-y-2">
                  {entity.details?.supportChannels?.map((channel, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">{channel}</span>
                    </div>
                  ))}
                  {entity.details?.supportEmail && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Email Support</span>
                    </div>
                  )}
                  {entity.details?.hasLiveChat && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Live Chat</span>
                    </div>
                  )}
                  {entity.documentationUrl && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Documentation</span>
                    </div>
                  )}
                  {entity.details?.communityUrl && (
                    <div className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-gray-600">Community Forum</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedResourceView; 